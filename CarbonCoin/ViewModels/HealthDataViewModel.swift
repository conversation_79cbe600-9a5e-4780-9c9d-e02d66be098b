//
//  HealthDataViewModel.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/19.
//

import Foundation
import SwiftUI

// MARK: - 健康数据统计ViewModel
@MainActor
class HealthDataViewModel: ObservableObject {
    // MARK: - Properties
    /// 数据类型
    let dataType: HealthDataType

    private var healthManager: HealthManager

    // MARK: - Published Properties
    /// 当前选中的时间段
    @Published var selectedPeriod: TimePeriod = .week

    /// 当前显示的健康数据
    @Published var currentHealthData: [HealthData] = []

    /// 统计信息
    @Published var statistics: SportsStatistics?

    /// 加载状态
    @Published var isLoading: Bool = false

    /// 错误信息
    @Published var errorMessage: String?

    /// Y轴最大值（用于图表刻度）
    @Published var yAxisMaxValue: Int = 15000

    /// Y轴最小值（用于图表刻度）
    @Published var yAxisMinValue: Int = 0


    // MARK: - Initialization
    /// 初始化器，需要指定数据类型
    init(dataType: HealthDataType, healthManager: HealthManager) {
        self.dataType = dataType
        self.healthManager = healthManager
    }
    
    // MARK: - Public Methods

    /// 更新 HealthManager 引用（用于从父视图注入正确的实例）
    func updateHealthManager(_ healthManager: HealthManager) {
        self.healthManager = healthManager
    }

    /// 初始化数据 (现在可以从外部调用)
    func initializeData() async {
        do {
            // 请求权限 - 使用当前数据类型
            try await healthManager.requestAuthorization(for: [dataType])
            // 加载数据
            await loadHealthData()
        } catch {
            errorMessage = "HealthKit 授权失败，请在设置中开启健康权限。"
        }
    }
    
    /// 加载健康数据（缓存感知）：
    /// 1) 若命中未过期缓存，立即展示，毫秒级响应；
    /// 2) 若缓存过期或未命中，后台拉取最新数据并刷新UI；
    private func loadHealthData() async {
        print("loadHealthData called, isLoading: \(isLoading), selectedPeriod: \(selectedPeriod)")
        errorMessage = nil

        // 读取缓存
        let cache = CacheManager.shared.read(type: dataType, period: selectedPeriod)
        if cache.hit {
            // 命中缓存，立即展示（不显示loading，保障切换无延迟）
            currentHealthData = cache.data
            calculateStatistics()
            updateYAxisScale()
            print("[VM] cache hit, expired=\(cache.expired), dataCount=\(cache.data.count)")

            // 若缓存已过期，则后台刷新
            if cache.expired {
                isLoading = false
                Task { [weak self] in
                    guard let self else { return }
                    do {
                        print("[VM] refreshing expired cache for \(self.dataType.displayName) @ \(self.selectedPeriod)")
                        let fresh = try await self.healthManager.fetchHealthData(type: self.dataType, period: self.selectedPeriod)
                        if fresh != self.currentHealthData {
                            self.currentHealthData = fresh
                            self.calculateStatistics()
                            self.updateYAxisScale()
                        }
                    } catch {
                        self.errorMessage = "获取健康数据失败，请稍后重试。"
                    }
                }
            } else {
                // 未过期，直接结束
                isLoading = false
            }
            return
        }

        // 无缓存：短暂显示loading并后台拉取
        isLoading = true
        Task { [weak self] in
            guard let self else { return }
            do {
                print("[VM] no cache, fetching fresh for \(self.dataType.displayName) @ \(self.selectedPeriod)")
                let fresh = try await self.healthManager.fetchHealthData(type: self.dataType, period: self.selectedPeriod)
                self.currentHealthData = fresh
                self.calculateStatistics()
                self.updateYAxisScale()
            } catch {
                self.errorMessage = "获取健康数据失败，请稍后重试。"
            }
            self.isLoading = false
            print("loadHealthData completed, isLoading: \(self.isLoading)")
        }
    }
    
    /// 计算统计信息
    private func calculateStatistics() {
        guard !currentHealthData.isEmpty else {
            statistics = nil
            return
        }

        // 根据数据类型计算统计信息
        switch dataType {
        case .steps:
            // 对于步数，转换为 StepData 并使用现有的统计逻辑
            let stepData = currentHealthData.map { $0.toStepData() }
            let previousPeriodAverage = calculatePreviousPeriodAverage()

            statistics = SportsStatistics(
                stepData: stepData,
                period: selectedPeriod,
                previousPeriodAverage: previousPeriodAverage
            )
        default:
            // 对于其他数据类型，暂时不实现统计计算
            fatalError("Statistics calculation not yet implemented for \(dataType)")
        }
    }
    
    /// 计算上一周期的平均值
    private func calculatePreviousPeriodAverage() -> Double? {
        // 简化实现：返回当前平均值的90%-110%作为模拟对比
        let currentAverage = currentHealthData.reduce(0) { $0 + $1.value } / Double(currentHealthData.count)
        let variation = Double.random(in: 0.9...1.1)
        return currentAverage * variation
    }
    
    /// 更新Y轴刻度
    private func updateYAxisScale() {
        guard !currentHealthData.isEmpty else {
            yAxisMinValue = 0
            yAxisMaxValue = 15000
            return
        }

        let values = currentHealthData.map { Int($0.value) }
        let minValue = values.min() ?? 0
        let maxValue = values.max() ?? 15000

        // 设置Y轴最小值（向下取整到千位）
        yAxisMinValue = max(0, (minValue / 1000) * 1000 - 1000)

        // 设置Y轴最大值（向上取整到千位）
        yAxisMaxValue = ((maxValue / 1000) + 1) * 1000 + 1000

        // 确保最小范围
        if yAxisMaxValue - yAxisMinValue < 5000 {
            yAxisMaxValue = yAxisMinValue + 5000
        }
    }

    /// 刷新数据
    func refreshData() async {
        await loadHealthData()
    }

    /// 时间段切换（由 UI 调用）
    func selectPeriod(_ period: TimePeriod) {
        print("[HealthDataViewModel] selectPeriod called with: \(period), current: \(selectedPeriod)")
        // 即时更新 selectedPeriod，以驱动 UI 状态同步
        selectedPeriod = period
        print("[HealthDataViewModel] selectedPeriod updated to: \(selectedPeriod)")
        // 使用缓存感知加载逻辑，做到毫秒级响应
        Task { [weak self] in
            await self?.loadHealthData()
        }
    }

    /// 为特定时间段加载数据
    func loadHealthDataForPeriod(_ period: TimePeriod) async {
        print("[HealthDataViewModel] loadHealthDataForPeriod called with: \(period)")
        selectedPeriod = period
        await loadHealthData()
    }

    /// 获取平均值（重命名为通用方法）
    func getAverageValue() -> Double {
        return statistics?.averageSteps ?? 0
    }

    /// 获取平均步数（保持兼容性）
    func getAverageSteps() -> Double {
        return getAverageValue()
    }

    /// 当前步数数据（保持兼容性）
    var currentStepData: [StepData] {
        return currentHealthData.map { $0.toStepData() }
    }

    /// 获取变化百分比文本
    func getChangePercentageText() -> String {
        guard let changePercentage = statistics?.changePercentage else {
            return "无数据"
        }

        let sign = changePercentage >= 0 ? "+" : ""
        return "\(sign)\(String(format: "%.1f", changePercentage))%"
    }

    /// 获取变化百分比颜色
    func getChangePercentageColor() -> Color {
        guard let changePercentage = statistics?.changePercentage else {
            return .textSecondary
        }

        return changePercentage >= 0 ? .green : .red
    }
}

// MARK: - Preview Support
extension HealthDataViewModel {
    
    /// 创建用于预览的实例
    static func preview(dataType: HealthDataType = .steps) -> HealthDataViewModel {
        let healthManager = HealthManager()
        let viewModel = HealthDataViewModel(dataType: dataType, healthManager: healthManager)

        // 生成预览数据
        let calendar = Calendar.current
        let now = Date()
        let startOfWeek = calendar.dateInterval(of: .weekOfYear, for: now)?.start ?? now

        var previewData: [HealthData] = []
        for i in 0..<7 {
            if let date = calendar.date(byAdding: .day, value: i, to: startOfWeek),
               date <= now {
                let value: Double
                switch dataType {
                case .steps:
                    value = Double(Int.random(in: 5000...12000))
                case .calories:
                    value = Double.random(in: 200...800)
                case .heartRate:
                    value = Double.random(in: 60...100)
                case .cyclingTime:
                    value = Double.random(in: 0...120)
                case .distance:
                    value = Double.random(in: 2...15)
                }
                previewData.append(HealthData(date: date, value: value, type: dataType))
            }
        }

        viewModel.currentHealthData = previewData
        viewModel.calculateStatistics()
        viewModel.updateYAxisScale()

        return viewModel
    }
}
