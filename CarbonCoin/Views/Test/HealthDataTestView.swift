////
////  HealthDataTestView.swift
////  CarbonCoin
////
////  Created by <PERSON><PERSON> on 2025/8/19.
////
//
//import SwiftUI
//
//// MARK: - 健康数据测试视图
///// 用于测试重构后的健康数据组件的扩展性
//struct HealthDataTestView: View {
//    
//    var body: some View {
//        NavigationStack {
//            ScrollView {
//                VStack(spacing: Theme.Spacing.lg) {
//                    // 测试步数数据（完全实现）
//                    VStack(alignment: .leading, spacing: Theme.Spacing.sm) {
//                        Text("步数统计（完全实现）")
//                            .font(.title2Brand)
//                            .foregroundColor(.textPrimary)
//                        
//                        HealthStatisticsCard(dataType: .steps, heal)
//                    }
//                    
//                    // 测试其他数据类型（显示占位符）
//                    VStack(alignment: .leading, spacing: Theme.Spacing.sm) {
//                        Text("其他健康数据类型（扩展接口）")
//                            .font(.title2Brand)
//                            .foregroundColor(.textPrimary)
//                        
//                        VStack(spacing: Theme.Spacing.md) {
//                            // 卡路里
//                            CompactHealthStatisticsCard(dataType: .calories)
//                            
//                            // 心率
//                            CompactHealthStatisticsCard(dataType: .heartRate)
//                            
//                            // 骑行时间
//                            CompactHealthStatisticsCard(dataType: .cyclingTime)
//                            
//                            // 距离
//                            CompactHealthStatisticsCard(dataType: .distance)
//                        }
//                    }
//                    
//                    // 扩展性说明
//                    VStack(alignment: .leading, spacing: Theme.Spacing.sm) {
//                        Text("重构成果")
//                            .font(.title2Brand)
//                            .foregroundColor(.textPrimary)
//                        
//                        VStack(alignment: .leading, spacing: Theme.Spacing.xs) {
//                            FeatureItem(
//                                icon: "checkmark.circle.fill",
//                                title: "通用化设计",
//                                description: "HealthDataViewModel 支持任意 HealthDataType"
//                            )
//                            
//                            FeatureItem(
//                                icon: "checkmark.circle.fill",
//                                title: "步数功能完整",
//                                description: "保持原有步数统计的所有功能"
//                            )
//                            
//                            FeatureItem(
//                                icon: "plus.circle.fill",
//                                title: "扩展接口就绪",
//                                description: "其他数据类型只需实现统计逻辑即可"
//                            )
//                            
//                            FeatureItem(
//                                icon: "arrow.triangle.2.circlepath",
//                                title: "依赖注入优化",
//                                description: "通过 @EnvironmentObject 传递 HealthManager"
//                            )
//                        }
//                        .padding(Theme.Spacing.md)
//                        .glassCard()
//                    }
//                    
//                    Spacer(minLength: 100)
//                }
//                .padding(.horizontal, Theme.Spacing.md)
//            }
//            .scrollContentBackground(.hidden)
//            .background(CustomAngularGradient())
//            .navigationTitle("健康数据测试")
//            .navigationBarTitleDisplayMode(.large)
//            .toolbarBackground(.clear, for: .navigationBar)
//            .toolbarColorScheme(.dark, for: .navigationBar)
//        }
//        .environmentObject(HealthManager())
//    }
//}
//
//// MARK: - 功能特性项组件
//struct FeatureItem: View {
//    let icon: String
//    let title: String
//    let description: String
//    
//    var body: some View {
//        HStack(spacing: Theme.Spacing.sm) {
//            Image(systemName: icon)
//                .font(.title3)
//                .foregroundColor(.brandColor)
//                .frame(width: 24)
//            
//            VStack(alignment: .leading, spacing: 2) {
//                Text(title)
//                    .font(.bodyBrand)
//                    .foregroundColor(.textPrimary)
//                
//                Text(description)
//                    .font(.captionBrand)
//                    .foregroundColor(.textSecondary)
//            }
//            
//            Spacer()
//        }
//    }
//}
//
//// MARK: - Preview
//#Preview {
//    HealthDataTestView()
//}
