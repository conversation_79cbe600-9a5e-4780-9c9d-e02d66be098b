//
//  HealthStatisticsCard.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/19.
//

import SwiftUI

// MARK: - 健康数据统计卡片组件
struct HealthStatisticsCard: View {

    // MARK: - Properties
    let dataType: HealthDataType
    private var healthManager: HealthManager
    
    @StateObject private var viewModel: HealthDataViewModel

    // MARK: - Initialization
    init(dataType: HealthDataType, healthManager: HealthManager) {
        self.dataType = dataType
        self.healthManager = healthManager
        self._viewModel = StateObject(wrappedValue: HealthDataViewModel(dataType: dataType, healthManager: healthManager))
    }
    
    // MARK: - Body
    var body: some View {
        Group {
            VStack(spacing: Theme.Spacing.md) {
                headerView(viewModel: viewModel)
                chartContainerView(viewModel: viewModel)
            }
            .padding(Theme.Spacing.lg)
            .glassCard()
            .refreshable {
                await viewModel.refreshData()
            }
        }
        .environmentObject(healthManager) // 将 healthManager 注入环境，供 HealthDataViewModel 使用
        .onAppear {
            Task {
                await viewModel.initializeData()
            }
        }
    }
    
    // MARK: - Header View
    private func headerView(viewModel: HealthDataViewModel) -> some View {
        HStack {
            VStack(alignment: .leading, spacing: Theme.Spacing.xs) {
                Text(viewModel.dataType.displayName + "统计")
                    .font(.title3Brand)
                    .foregroundColor(.textPrimary)

                if let statistics = viewModel.statistics {
                    HStack(spacing: Theme.Spacing.xs) {
                        Text("\(viewModel.getChangePercentageText())")
                            .font(.captionBrand)
                            .foregroundColor(viewModel.getChangePercentageColor())
                    }
                }
            }

            Spacer()

            CompactTimePeriodSelector(selectedPeriod: $viewModel.selectedPeriod) { period in
                viewModel.selectPeriod(period)
            }
        }
    }
    
    // MARK: - Chart Container View
    private func chartContainerView(viewModel: HealthDataViewModel) -> some View {
        VStack(spacing: Theme.Spacing.md) {
            chartView(viewModel: viewModel)
            statisticsView(viewModel: viewModel)
        }
    }
    
    // MARK: - Chart View
    private func chartView(viewModel: HealthDataViewModel) -> some View {
        VStack(spacing: Theme.Spacing.sm) {
            if viewModel.isLoading {
                ProgressView("加载中...")
                    .frame(height: 200)
            } else if let errorMessage = viewModel.errorMessage {
                VStack(spacing: Theme.Spacing.sm) {
                    Image(systemName: "exclamationmark.triangle")
                        .font(.title2)
                        .foregroundColor(.orange)
                    
                    Text(errorMessage)
                        .font(.bodyBrand)
                        .foregroundColor(.textSecondary)
                        .multilineTextAlignment(.center)
                }
                .frame(height: 200)
            } else {
                LineChartView(
                    healthData: viewModel.currentHealthData,
                    averageValue: viewModel.getAverageValue(),
                    yAxisMin: Double(viewModel.yAxisMinValue),
                    yAxisMax: Double(viewModel.yAxisMaxValue),
                    selectedPeriod: viewModel.selectedPeriod
                )
                .frame(height: 200)
            }
        }
    }
    
    // MARK: - Statistics View
    private func statisticsView(viewModel: HealthDataViewModel) -> some View {
        HStack(spacing: Theme.Spacing.lg) {
            if let statistics = viewModel.statistics {
                // 根据数据类型显示不同的统计项
                switch viewModel.dataType {
                case .steps:
                    statisticItem(
                        title: "总步数",
                        value: formatNumber(statistics.totalSteps),
                        icon: "figure.walk"
                    )

                    Divider()
                        .frame(height: 30)
                        .background(Color.textSecondary.opacity(0.3))

                    statisticItem(
                        title: "平均",
                        value: formatNumber(Int(statistics.averageSteps)),
                        icon: "chart.line.uptrend.xyaxis"
                    )

                    Divider()
                        .frame(height: 30)
                        .background(Color.textSecondary.opacity(0.3))

                    statisticItem(
                        title: "最高",
                        value: formatNumber(statistics.maxSteps),
                        icon: "arrow.up.circle"
                    )
                default:
                    // 对于其他数据类型，显示占位符
                    Text("暂未支持")
                        .font(.bodyBrand)
                        .foregroundColor(.textSecondary)
                }
            }
        }
        .padding(.horizontal, Theme.Spacing.sm)
    }
    
    // MARK: - Statistic Item
    private func statisticItem(title: String, value: String, icon: String) -> some View {
        VStack(spacing: Theme.Spacing.xs) {
            HStack(spacing: Theme.Spacing.xs) {
                Image(systemName: icon)
                    .font(.caption)
                    .foregroundColor(.brandColor)
                
                Text(title)
                    .font(.captionBrand)
                    .foregroundColor(.textSecondary)
            }
            
            Text(value)
                .font(.bodyBrand)
                .foregroundColor(.textPrimary)
        }
        .frame(maxWidth: .infinity)
    }
    
    // MARK: - Helper Methods
    private func formatNumber(_ number: Int) -> String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .decimal
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter.string(from: NSNumber(value: number)) ?? "\(number)"
    }
}

// MARK: - Compact Version
struct CompactHealthStatisticsCard: View {

    // MARK: - Properties
    let dataType: HealthDataType
    private var healthManager: HealthManager
    @StateObject private var viewModel: HealthDataViewModel

    // MARK: - Initialization
    init(dataType: HealthDataType, healthManager: HealthManager) {
        self.dataType = dataType
        self.healthManager = healthManager
        self._viewModel = StateObject(wrappedValue: HealthDataViewModel(dataType: dataType, healthManager: healthManager))
    }
    
    var body: some View {
        Group {
                VStack(spacing: Theme.Spacing.sm) {
                    HStack {
                        Text(viewModel.dataType.displayName)
                            .font(.bodyBrand)
                            .foregroundColor(.textPrimary)

                        Spacer()

                        CompactTimePeriodSelector(selectedPeriod: $viewModel.selectedPeriod) { period in
                            viewModel.selectPeriod(period)
                        }
                    }

                    if let statistics = viewModel.statistics {
                        // 根据数据类型显示不同的内容
                        switch viewModel.dataType {
                        case .steps:
                            HStack {
                                VStack(alignment: .leading, spacing: 2) {
                                    Text("\(formatNumber(Int(statistics.averageSteps)))")
                                        .font(.title2Brand)
                                        .foregroundColor(.textPrimary)

                                    Text("平均步数")
                                        .font(.captionBrand)
                                        .foregroundColor(.textSecondary)
                                }

                                Spacer()

                                Text(viewModel.getChangePercentageText())
                                    .font(.captionBrand)
                                    .foregroundColor(viewModel.getChangePercentageColor())
                            }
                        default:
                            // 对于其他数据类型，显示占位符
                            Text("暂未支持")
                                .font(.bodyBrand)
                                .foregroundColor(.textSecondary)
                        }
                    }
                }
                .padding(Theme.Spacing.md)
                .glassCard()
            }
        .environmentObject(healthManager)
        .onAppear {
            Task {
                await viewModel.initializeData()
            }
        }
    }
    
    // MARK: - Helper Methods
    private func formatNumber(_ number: Int) -> String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .decimal
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter.string(from: NSNumber(value: number)) ?? "\(number)"
    }
}

// MARK: - Preview
#Preview {
    let healthManager = HealthManager()
    VStack(spacing: 20) {
        HealthStatisticsCard(dataType: .steps, healthManager: healthManager)
        CompactHealthStatisticsCard(dataType: .steps, healthManager: healthManager)
    }
    .padding()
    .stableBackground()
    .environmentObject(HealthManager())
}
